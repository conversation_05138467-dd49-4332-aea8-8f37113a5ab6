# 游戏编辑功能文件位置说明

## 概述

本文档详细介绍了修改版本中各种编辑功能的文件位置和实现方式，包括技能编辑、魂卡编辑、载具编辑、巅峰加点、尸宠编辑等核心功能。

## 📁 文件结构总览

```
scripts修改版/
├── UI/
│   ├── base/btnList/BtnList.as              # 🔥 核心编辑入口
│   ├── bag/ItemsGripBtnListCtrl.as          # 物品编辑控制器
│   ├── edit/                                # 编辑功能界面
│   ├── pet/evo/PetEvoBoard.as              # 尸宠编辑界面
│   ├── peak/PeakChildBoard.as              # 巅峰加点界面
│   └── vehicle/VehicleEvolutionBoard.as     # 载具编辑界面
├── dataAll/
│   ├── _app/edit/                          # 编辑数据核心
│   ├── _app/peak/PeakData.as              # 巅峰系统数据
│   └── pet/PetData.as                     # 尸宠数据
```

## 🎯 各功能详细位置

### 1. 技能编辑 (Skill Edit)

#### 核心文件
- **主入口**: `scripts修改版/UI/bag/ItemsGripBtnListCtrl.as` (第1036-1054行)
- **编辑逻辑**: `scripts修改版/UI/base/btnList/BtnList.as` (EquipEdit方法)
- **界面控制**: `scripts修改版/UI/edit/BosseditEditBoard.as` (第377-392行)

#### 功能特点
```actionscript
// 技能管理编辑命令
"09*技能名称" 或 "技能添加*技能名称"    # 添加单个技能
"09*全部" 或 "技能添加*全部"           # 添加所有技能
"10*技能名称" 或 "技能删除*技能名称"    # 删除技能
```

#### 常用技能代码
- **头盔/腰带**: `godHand_equip`, `immune_equip`
- **战衣/裤子**: `sacrifice_equip`, `backStrong_equip`
- **时装**: `summonWolf_bigBoss`, `zoomOut`

### 2. 魂卡编辑 (Soul Card Edit)

#### 核心文件
- **主界面**: `scripts修改版/UI/edit/BosseditCardBoard.as`
- **编辑方法**: `BossCaedCheating()` (第670-698行)
- **数据结构**: `scripts修改版/dataAll/_app/edit/card/BossCardSave.as`

#### 编辑命令
```actionscript
"00*数值" 或 "魂卡星级*数值"     # 修改魂卡星级
"01*数值" 或 "生命系数*数值"     # 修改生命系数
"02*数值" 或 "伤害系数*数值"     # 修改伤害系数
"03*文本" 或 "魂卡代码*文本"     # 修改魂卡代码
"04*可空" 或 "复制代码*可空"     # 复制当前魂卡代码
"05*数值" 或 "魂卡ID*数值"      # 修改魂卡ID
"06*可空" 或 "技能编辑*可空"     # 进入技能编辑
"07*可空" 或 "提升人物属性*可空"  # 提升人物属性
```

#### 使用方法
1. 选择要编辑的魂卡
2. 点击"remake"按钮
3. 输入对应的编辑命令
4. 确认修改

### 3. 载具编辑 (Vehicle Edit)

#### 核心文件
- **主界面**: `scripts修改版/UI/vehicle/VehicleEvolutionBoard.as`
- **编辑方法**: `VehicleEdit()` (第174-196行)
- **入口**: `scripts修改版/UI/base/btnList/BtnList.as` (第368行)

#### 编辑命令
```actionscript
"00*数值" 或 "碾压攻击*数值"     # 修改碾压攻击等级
"01*数值" 或 "机枪攻击*数值"     # 修改机枪攻击等级
"02*数值" 或 "主炮攻击*数值"     # 修改主炮攻击等级
```

#### 使用方法
1. 进入载具界面
2. 选择要编辑的载具
3. 使用载具编辑功能
4. 输入编辑命令（支持&分隔多个命令）

### 4. 巅峰加点 (Peak Points)

#### 核心文件
- **数据核心**: `scripts修改版/dataAll/_app/peak/PeakData.as`
- **界面**: `scripts修改版/UI/peak/PeakUI.as`
- **子界面**: `scripts修改版/UI/peak/PeakChildBoard.as` (第224-229行)

#### 功能特点
- **等级管理**: 巅峰等级和经验控制
- **点数分配**: 8种人物属性加点
- **技能升级**: 2种人物技能（每级消耗2点）
- **重置功能**: 无限重置巅峰点数

#### 编辑方法
```actionscript
// 直接设置巅峰点数
private function yueying(str:String) : void
{
    Gaming.PG.SAVE.peak.dpN = Number(str);
    this.fleshData();
    Gaming.uiGroup.alertBox.showSuccess("设置加点成功!");
}
```

### 5. 尸宠编辑 (Pet Edit)

#### 核心文件
- **主界面**: `scripts修改版/UI/pet/evo/PetEvoBoard.as`
- **编辑方法**: `Pet_DingCheating()` (第203-250行)
- **数据结构**: `scripts修改版/dataAll/pet/PetData.as`

#### 编辑命令
```actionscript
"00*数量" 或 "等级*数量"        # 修改尸宠等级
"01*数量" 或 "经验*数量"        # 修改尸宠经验
"02*文本" 或 "名称*文本"        # 修改尸宠名称
"03*数量" 或 "生命*数量"        # 修改生命加成
"04*数量" 或 "伤害*数量"        # 修改伤害加成
"05*数量" 或 "头防*数量"        # 修改头部防御
"06*英文" 或 "颜色*英文"        # 修改尸宠颜色
"07*可空" 或 "出战*可空"        # 设置出战状态
"08*可空" 或 "替补*可空"        # 设置替补状态
"09*可空" 或 "资质编辑*可空"     # 资质编辑
"10*可空" 或 "资质强化*可空"     # 资质强化
```

## 🔧 编辑功能调用流程

### 通用编辑流程

```mermaid
graph TD
    A[选择物品/角色] --> B[右键菜单]
    B --> C[选择编辑选项]
    C --> D[弹出编辑对话框]
    D --> E[输入编辑命令]
    E --> F[解析命令参数]
    F --> G[执行修改逻辑]
    G --> H[更新数据]
    H --> I[刷新界面]
    I --> J[显示成功提示]
```

### 编辑入口统一管理

**文件**: `scripts修改版/UI/base/btnList/BtnList.as`

```actionscript
// 编辑功能回调注册
ExternalInterface.addCallback("armsEdit", ArmsEdit);        // 武器编辑
ExternalInterface.addCallback("equipEdit", EquipEdit);      // 装备编辑
ExternalInterface.addCallback("vehicleEdit", VehicleEdit);  // 载具编辑
ExternalInterface.addCallback("deviceEdit", DeviceEdit);    // 装置编辑
ExternalInterface.addCallback("jewelryEdit", JewelryEdit);  // 饰品编辑
ExternalInterface.addCallback("shieldEdit", ShieldEdit);    // 护盾编辑
```

## 📋 编辑命令规范

### 命令格式
- **基本格式**: `代码*参数` 或 `中文名称*参数`
- **多命令**: 使用`&`分隔多个命令
- **空参数**: 使用`*可空`表示无需参数

### 参数类型
- **数值**: 整数或小数
- **文本**: 字符串内容
- **可空**: 不需要参数的命令
- **英文**: 特定的英文代码

## 🛠️ 开发者注意事项

### 代码修改要点
1. **数据验证**: 确保输入参数的有效性
2. **错误处理**: 添加适当的错误提示
3. **界面刷新**: 修改后及时更新显示
4. **存档兼容**: 保持与原版存档的兼容性

### 扩展功能建议
1. **批量编辑**: 支持一次性编辑多个对象
2. **模板保存**: 保存常用的编辑配置
3. **撤销功能**: 支持编辑操作的撤销
4. **导入导出**: 支持编辑数据的导入导出

## 🔍 故障排除

### 常见问题

**Q: 编辑命令无效？**
A: 检查命令格式是否正确，参数类型是否匹配。

**Q: 修改后没有生效？**
A: 确认是否调用了界面刷新方法，检查数据是否正确保存。

**Q: 游戏崩溃？**
A: 检查参数范围是否合理，避免设置过大或过小的数值。

**Q: 存档损坏？**
A: 建议修改前备份存档，使用合理的参数值。

## 📝 更新日志

### v1.0 (修改版)
- ✅ 实现基础编辑功能
- ✅ 统一编辑命令格式
- ✅ 添加中文命令支持

### v1.1 (玉帝后台版)
- ✅ 优化编辑界面
- ✅ 增强错误处理
- ✅ 添加更多编辑选项

---

**注意**: 使用编辑功能前请备份存档，避免因误操作导致数据丢失。如需了解更多技术细节，请参考对应的源代码文件。
