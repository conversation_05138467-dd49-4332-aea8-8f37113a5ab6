# 角色技能编辑详细说明

## 概述

角色技能编辑是修改版本中的重要功能，允许玩家直接修改角色已学习技能的等级、熟练度等属性，以及删除不需要的技能。这个功能主要针对的是**角色自身学习的技能**，而不是装备附带的技能。

## 🎯 技能类型说明

### 角色技能 vs 装备技能

| 特性 | 角色技能 (Hero Skill) | 装备技能 (Equipment Skill) |
|------|----------------------|---------------------------|
| **获得方式** | 通过学习、探索获得 | 装备自带或编辑添加 |
| **持久性** | 永久学会，不会丢失 | 依赖装备，卸下装备失效 |
| **升级方式** | 消耗材料和金币升级 | 通过装备强化或编辑 |
| **编辑位置** | 技能界面 | 装备编辑界面 |
| **数据存储** | 角色数据 | 装备数据 |

### 角色技能分类

#### 1. 按释放方式分类
- **主动技能** (Active): 需要玩家手动释放
  - 例如: 治疗术、火球术、闪现等
  - 有冷却时间和消耗
- **被动技能** (Passive): 自动生效
  - 例如: 生命提升、攻击力加成等
  - 学会后持续生效
- **状态技能** (State): 产生持续效果
  - 例如: 护盾、增益状态等
- **瞬发技能** (Instant): 瞬间释放生效
  - 例如: 瞬间回血、瞬间传送等

#### 2. 按技能来源分类
- **基础技能**: 角色初始可学习的技能
- **进阶技能**: 需要达到一定等级才能学习
- **特殊技能**: 通过特殊途径获得
- **天赋技能**: 角色固有技能，无法删除

## 📁 核心文件位置

### 主要文件结构
```
scripts修改版/
├── UI/skill/
│   ├── SkillUpgradeUI.as              # 🔥 技能编辑主界面
│   ├── SkillExploreUI.as              # 技能探索界面
│   └── SkillUI.as                     # 技能总界面
├── dataAll/skill/
│   ├── HeroSkillData.as               # 角色技能数据
│   ├── HeroSkillDataGroup.as          # 技能组管理
│   ├── define/
│   │   ├── HeroSkillDefine.as         # 技能定义
│   │   ├── SkillDefine.as             # 基础技能定义
│   │   └── SkillDefineGroup.as        # 技能定义组
│   └── save/
│       └── HeroSkillSave.as           # 技能存档数据
```

### 关键编辑方法

**文件**: `scripts修改版/UI/skill/SkillUpgradeUI.as`

<augment_code_snippet path="scripts修改版/UI/skill/SkillUpgradeUI.as" mode="EXCERPT">
```actionscript
public function SkillCheating(str0:String) : void
{
    var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
    var Arr_Skill:Array = str0.split("*", str0.length);
    var Title:String = Arr_Skill[0];
    var NumericalValue:int = int(Arr_Skill[1]);
    
    if(Title == "技能等级" || Title == "00")
    {
        da0.save.lv = Number(NumericalValue);
        Gaming.uiGroup.alertBox.showSuccess("设置技能等级成功！");
    }
    // ... 其他编辑功能
}
```
</augment_code_snippet>

## 🔧 编辑命令详解

### 基本命令格式
- **格式**: `命令代码*参数` 或 `中文名称*参数`
- **分隔符**: 使用 `*` 分隔命令和参数
- **参数类型**: 数值、文本或可空

### 详细命令列表

#### 1. 技能等级编辑
```actionscript
"00*数值" 或 "技能等级*数值"
```
- **功能**: 直接设置技能等级
- **参数**: 1-技能最大等级的整数
- **示例**: `00*10` 或 `技能等级*10`
- **注意**: 不同技能有不同的最大等级限制

#### 2. 技能熟练度编辑
```actionscript
"01*数值" 或 "技能熟练度*数值"
```
- **功能**: 设置技能的总熟练度
- **参数**: 0或正整数
- **示例**: `01*1000` 或 `技能熟练度*1000`
- **说明**: 熟练度影响技能升级的进度

#### 3. 今日熟练度编辑
```actionscript
"02*数值" 或 "今日熟练度*数值"
```
- **功能**: 设置当日获得的熟练度
- **参数**: 0或正整数
- **示例**: `02*100` 或 `今日熟练度*100`
- **说明**: 每日熟练度有上限限制

#### 4. 删除技能
```actionscript
"03*可空" 或 "删除当前技能*可空"
```
- **功能**: 删除当前选中的技能
- **参数**: 无需参数，使用 `*可空`
- **示例**: `03*可空` 或 `删除当前技能*可空`
- **警告**: 天赋技能无法删除

#### 5. 复制技能代码
```actionscript
"04*可空" 或 "复制当前技能代码*可空"
```
- **功能**: 复制当前技能的内部代码
- **参数**: 无需参数
- **示例**: `04*可空` 或 `复制当前技能代码*可空`
- **用途**: 用于技能分析或其他编辑功能

## 🎮 使用方法

### 步骤详解

#### 1. 进入技能编辑界面
1. 打开游戏主界面
2. 点击"技能"按钮进入技能界面
3. 选择要编辑的技能
4. 点击技能升级或详情按钮
5. 在技能详情界面找到编辑选项

#### 2. 执行编辑命令
1. 点击编辑按钮（通常显示为"编辑"或特殊图标）
2. 在弹出的输入框中输入编辑命令
3. 确认输入，系统会显示执行结果
4. 界面会自动刷新显示新的技能数据

#### 3. 验证修改结果
1. 检查技能等级是否正确修改
2. 查看技能描述是否更新
3. 测试技能效果是否符合预期
4. 确认修改已保存到存档

### 实际操作示例

#### 示例1: 提升技能等级
```
目标: 将"治疗术"技能等级提升到10级
操作: 选择治疗术 → 点击编辑 → 输入"00*10" → 确认
结果: 技能等级变为10级，效果大幅提升
```

#### 示例2: 删除不需要的技能
```
目标: 删除学错的"火球术"技能
操作: 选择火球术 → 点击编辑 → 输入"03*可空" → 确认
结果: 火球术从技能列表中移除
```

#### 示例3: 快速提升熟练度
```
目标: 快速提升技能熟练度以便升级
操作: 选择目标技能 → 点击编辑 → 输入"01*9999" → 确认
结果: 技能熟练度大幅提升，可以升级
```

## ⚠️ 注意事项

### 安全使用建议

#### 1. 备份存档
- 使用编辑功能前务必备份存档
- 建议在测试环境中先尝试
- 避免在重要存档上直接操作

#### 2. 参数范围
- **技能等级**: 不要超过技能的最大等级限制
- **熟练度**: 避免设置过大的数值
- **负数**: 不要使用负数参数

#### 3. 技能限制
- **天赋技能**: 无法删除，尝试删除会失败
- **前置条件**: 某些技能有学习前置条件
- **冲突技能**: 某些技能之间可能存在冲突

### 常见问题解决

#### Q: 编辑后技能等级没有变化？
A: 检查是否超过了技能的最大等级限制，或者参数格式是否正确。

#### Q: 删除技能失败？
A: 可能是天赋技能或有特殊保护的技能，这类技能无法删除。

#### Q: 技能效果异常？
A: 可能是等级设置过高，建议重新设置合理的等级。

#### Q: 修改后游戏崩溃？
A: 立即恢复备份存档，检查是否使用了不合理的参数。

## 🔍 技术细节

### 数据结构说明

#### HeroSkillData 主要属性
- `save.lv`: 技能等级
- `save.profi`: 技能熟练度
- `save.dayProfi`: 今日熟练度
- `save.lockB`: 是否为锁定技能（天赋技能）
- `save.baseLabel`: 技能基础标识

#### 技能定义系统
- **HeroSkillDefine**: 定义技能的基本属性
- **SkillDefineGroup**: 管理所有技能定义
- **技能分类**: 按类型、等级、获得方式分类

### 扩展功能建议

#### 1. 批量编辑
- 同时编辑多个技能
- 技能等级批量设置
- 熟练度批量清零

#### 2. 技能模板
- 保存常用的技能配置
- 快速应用技能模板
- 分享技能配置

#### 3. 高级功能
- 技能效果预览
- 技能冲突检测
- 技能推荐系统

---

**重要提醒**: 角色技能编辑功能强大但需谨慎使用。建议在充分了解游戏机制的基础上进行编辑，避免破坏游戏平衡性。使用前请务必备份存档！
